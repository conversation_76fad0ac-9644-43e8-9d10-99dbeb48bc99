import { printLabel } from "@/components/LBL/labelUtilV2";
import ArtVisitLbl from "./ArtVisitLbl.vue";
import PatientDemographicLbl from "./PatientDemographicLbl.vue";
import TransferoutLbl from "./TransferoutLbl.vue";
import { PatientService } from "@/services/patient_service";
import FilingNumber from "@/apps/ART/Labels/FilingNumberLbl.vue";
import DrugLbl from "./DrugLbl.vue";
import NpidLbl from "./NpidLbl.vue";

export function printArtDrug(drugID: number, quantity: number) {
    printLabel(DrugLbl, { scaleHeight: 316, lblUrl: `drugs/${drugID}/barcode?quantity=${quantity}` });
}

export function printArtFilingNumberLbl(patientID: number) {
    printLabel(FilingNumber, { scaleHeight: 316, lblUrl: `patients/${patientID}/labels/filing_number` });
}

export function printArtVisitLbl(patientID: number, date = PatientService.getSessionDate()) {
    printLabel(ArtVisitLbl, {
        copies: 2,
        lblUrl: `programs/${PatientService.getProgramID()}/patients/${patientID}/labels/visits?date=${date}`,
    });
}

export function printArtPatientDemographicsLbl(patientID: number) {
    printLabel(PatientDemographicLbl, {
        lblUrl: `programs/${PatientService.getProgramID()}/patients/${patientID}/labels/patient_history`,
    });
}

export function printArtTransferoutLbl(patientID: number, date = PatientService.getSessionDate()) {
    printLabel(TransferoutLbl, { lblUrl: `programs/1/patients/${patientID}/labels/transfer_out?date=${date}` });
}

export async function printNpidLbl(patientID: number, generateQrCode = false) {
    return printLabel(NpidLbl, {
        scaleHeight: 280,
        useImage: false,
        lblUrl: `patients/${patientID}/labels/national_health_id?qr_code=${generateQrCode}`,
    });
}
