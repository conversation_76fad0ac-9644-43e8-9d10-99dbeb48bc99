import legacy from "@vitejs/plugin-legacy";
import vue from "@vitejs/plugin-vue";
import path from "path";
import { defineConfig } from "vite";
import { visualizer } from "rollup-plugin-visualizer";
import basicSsl from "@vitejs/plugin-basic-ssl";

export default defineConfig(({ command, mode }) => {
    return {
        plugins: [vue(), legacy(), visualizer()],
        resolve: {
            alias: {
                "@": path.resolve(__dirname, "./src"),
            },
        },
        test: {
            globals: true,
            environment: "jsdom",
        },
        build: {
            rollupOptions: {
                output: {
                    manualChunks(id) {
                        if (id.includes("node_modules")) {
                            if (id.includes("lodash")) {
                                return "lodash"; // Separate lodash into its own chunk
                            }
                            if (id.includes("apexcharts")) {
                                return "apexcharts"; // Separate apexcharts into its own chunk
                            }
                            if (id.includes("chart.js")) {
                                return "chartjs"; // Separate chart.js into its own chunk
                            }
                            if (id.includes("vue3-barcode-qrcode-reader")) {
                                return "barcode-qrcode-reader"; // Separate vue3-barcode-qrcode-reader into its own chunk
                            }
                            return "vendor"; // Move all other dependencies into a 'vendor' chunk
                        }
                    },
                },
            },
        },
        base: mode === "test" ? "" : "",
    };
});
