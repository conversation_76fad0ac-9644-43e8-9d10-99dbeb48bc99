/**
 * Checks if the path is an external link.
 *
 * @param path - The path to check.
 * @returns true when the path is an external link. Otherwise, false.
 */
export function isExternal(path: string): boolean {
    return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * Resolves the complete path based on the provided route path and base path.
 *
 * @param routePath - The path of the route.
 * @param basePath - The base path.
 * @returns The resolved path.
 */
export function resolvePath(routePath: string, basePath: string): string {
    if (isExternal(routePath)) return routePath;
    if (isExternal(basePath)) return basePath;
    const path = `${basePath}/${routePath}`;
    return "/" + path.split("/").filter(Boolean).join("/");
}

/**
 * Constructs a parameterized URL by appending query parameters to the given URI.
 *
 * @param uri - The base URI to which parameters will be added.
 * @param params - An optional object containing key-value pairs of parameters.
 *                 The values can be of type string, boolean, or number.
 * @returns The parameterized URL with appended query parameters.
 */
export function parameterizeUrl(uri: string, params?: any) {
    if (params)
        return (
            uri +
            "?" +
            Object.entries(params)
                .map(([key, value]) => `${key}=${value}`)
                .join("&")
        );
    return uri;
}

/**
 * Converts an object to a URL parameter string.
 *
 * @param obj - The object to convert to URL parameters.
 * @returns The URL parameter string.
 */
function parameterizeObjToString(obj: Record<string, any>) {
    let str = "";
    for (const [key, value] of Object.entries(obj)) {
        str += `${key}=${value}&`;
    }
    return str;
}

function updateUrlParamString(url: string, params = {}) {
    const [urlPath, urlStrParams] = url.split("?");
    const urlParams = `${urlStrParams ?? ""}`.split("&").reduce((a: any, c: any) => {
        const [key, value] = c.split("=");
        return { ...a, [key]: value };
    }, params);
    return `${urlPath}?${parameterizeObjToString(urlParams)}`;
}

export default {
    parameterizeObjToString,
    updateUrlParamString,
    parameterizeUrl,
    resolvePath,
    isExternal,
};
