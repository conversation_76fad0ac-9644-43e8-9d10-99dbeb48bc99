<template>
  <div class="network-config">
    <!-- Header Section -->
    <ion-row>
      <ion-col size="12">
        <div class="header">
          <div class="title-section">
            <h3 class="title">Network Configuration</h3>
            <span class="subtitle">Configure offline connection settings</span>
          </div>
        </div>
      </ion-col>
    </ion-row>

    <!-- Toggle Controls Section -->
    <ion-row class="controls-section">
      <!-- Server Mode Toggle -->
      <ion-col size="12" size-md="6">
        <div class="toggle-container">
          <div class="toggle-section">
            <span class="toggle-label">Server Mode:</span>
            <div class="toggle-wrapper">
              <label class="switch">
                <input type="checkbox" v-model="isLiveServer" @change="handleServerModeChange">
                <span class="slider round"></span>
              </label>
              <span class="toggle-status">{{ isLiveServer ? 'Live Server' : 'LAN Server' }}</span>
            </div>
          </div>
          <p class="mode-description">
            {{ isLiveServer
              ? 'Live Server mode uses internet connection for data synchronization'
              : 'LAN Server mode uses local network connection for data synchronization'
            }}
          </p>
        </div>
      </ion-col>

      <!-- Cache Patient Records Toggle -->
      <ion-col size="12" size-md="6">
        <div class="toggle-container">
          <div class="toggle-section">
            <span class="toggle-label">Cache Patient Records:</span>
            <div class="toggle-wrapper">
              <label class="switch">
                <input type="checkbox" v-model="storeCachedRecords" @change="handleCacheToggleChange">
                <span class="slider round"></span>
              </label>
              <span class="toggle-status">{{ storeCachedRecords ? 'YES' : 'NO' }}</span>
            </div>
          </div>
          <p class="mode-description">
            {{ storeCachedRecords
                ? 'Patient records will be cached locally for offline access'
                : 'Patient records will not be stored locally'
              }}
          </p>
        </div>
      </ion-col>
    </ion-row>

    <!-- Network Settings Section -->
    <ion-row class="network-settings">
      <!-- IP Address -->
      <ion-col size="12" size-md="6">
        <div>
          <label for="ip-address" class="label">IP Address</label>
          <input
            id="ip-address"
            v-model="ipAddress"
            type="text"
            :placeholder="isLiveServer ? '***********' : '***********'"
            class="input"
            :class="{ 'error': ipError }"
            @blur="validateIp"
            @input="clearIpError"
          />
          <span v-if="ipError" class="error-message">{{ ipError }}</span>
        </div>
      </ion-col>

      <!-- Port Number -->
      <ion-col size="12" size-md="6">
        <div>
          <label for="port" class="label">Port Number</label>
          <input
            id="port"
            v-model="port"
            type="number"
            placeholder="3002"
            min="1"
            max="65535"
            class="input"
            :class="{ 'error': portError }"
            @blur="validatePort"
            @input="clearPortError"
          />
          <span v-if="portError" class="error-message">{{ portError }}</span>
        </div>
      </ion-col>
    </ion-row>

    <!-- Connection String Display -->
    <ion-row v-if="isValid" class="connection-section">
      <ion-col size="12">
        <div class="connection-result">
          <div class="connection-string">
            <strong>Connection String:</strong> {{ connectionString }}
          </div>
          <div v-if="connectionStatus" class="connection-status">
            <div :class="['status-indicator', connectionStatus]">
              {{ connectionStatus === 'available' ? 'Connected' : 'Connection Failed' }}
            </div>
            <div v-if="connectionStatus === 'available'" class="timestamp">
              Last checked: {{ connectionTimestamp }}
            </div>
          </div>
        </div>
      </ion-col>
    </ion-row>

    <!-- Action Buttons -->
    <ion-row class="actions-section">
      <ion-col size="12" size-md="6">
        <ion-button 
          @click="handleTestConnection"
          :disabled="!isValid || isTestingConnection"
          class="test-btn"
          :class="{ 'disabled': !isValid || isTestingConnection }"
          expand="block"
          fill="outline"
          style="width: 100%;"
        >
          {{ isTestingConnection ? 'Testing...' : 'Test Connection' }}
        </ion-button>
      </ion-col>
      
      <ion-col size="12" size-md="6">
        <ion-button
          v-if="showSaveButton" 
          @click="handleSave"
          :disabled="!isValid"
          class="save-btn"
          :class="{ 'disabled': !isValid }"
          expand="block"
          style="width: 100%;"
        >
          Save Configuration
        </ion-button>
      </ion-col>
    </ion-row>
  </div>
</template>
  
<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { toastWarning, toastSuccess, toastDanger } from "@/utils/Alerts";
import { updateConnectionString } from "@/services/offline_service";
  
  // Reactive data
  const ipAddress = ref('0.0.0.0') as any
  const port = ref('3002') as any
  const ipError = ref('') as any
  const portError = ref('') as any
  const isLiveServer = ref(true) as any
  const isTestingConnection = ref(false) as any
  const connectionStatus = ref(null) as any
  const connectionTimestamp = ref(null) as any
  const LAST_TEST_KEY = 'network_last_test_result'
  const isInitialized = ref(false)
  const isCachingEnabled = ref()
  const showSaveButton = ref(true);
  
  // Connection timeout constant
  const CONNECTION_TIMEOUT = 5000; // 5 seconds timeout

  // Computed properties
  const isValid = computed(() => {
    return isValidIp(ipAddress.value) && isValidPort(port.value) && !ipError.value && !portError.value
  })

  const connectionString = computed(() => {
    return `${ipAddress.value}:${port.value}`
  })

  // Computed property to get the full connection URL
  const fullConnectionUrl = computed(() => {
    return `https://${ipAddress.value}:${port.value}/api/v1`
  })

  // Computed property to sync with localStorage for offline_connection_string
  const persistedConnectionString = computed({
    get: () => {
      return localStorage.getItem('offline_connection_string') || ''
    },
    set: (value: string) => {
      if (value) {
        localStorage.setItem('offline_connection_string', value)
      }
    }
  })

  // Watch server mode changes and update useMODS localStorage
  watch(isLiveServer, (newVal) => {
    // Only execute if component is initialized to avoid overriding loaded values
    if (!isInitialized.value) return
    
    // Set useMODS to true for LAN Server (local), false for Live Server
    localStorage.setItem('useMODS', (!newVal).toString())
    
    if (!newVal) {
      ipAddress.value = '0.0.0.0'
      ipError.value = ''
    }
  })

  // Watch for changes in connection string and persist them
  watch(fullConnectionUrl, (newUrl) => {
    if (isInitialized.value && isValid.value) {
      persistedConnectionString.value = newUrl
    }
  })

  // Function to update useMODS in localStorage
  const updateUseMODS = (isLiveServerMode: boolean) => {
    // useMODS = true when LAN Server (local), false when Live Server
    const useMODS = !isLiveServerMode
    localStorage.setItem('useMODS', useMODS.toString())
    console.log(`useMODS set to: ${useMODS} (${isLiveServerMode ? 'Live Server' : 'LAN Server'} mode)`)
  }

  // IP validation function
  const isValidIp = (ip: any) => {
    if (!ip) return false
    if (ip === 'localhost') return true
    
    const parts = ip.split('.')
    if (parts.length !== 4) return false
    
    return parts.every((part: any) => {
      const num = parseInt(part, 10)
      return !isNaN(num) && num >= 0 && num <= 255 && part === num.toString()
    })
  }

  // Port validation function
  const isValidPort = (portNum: any) => {
    if (!portNum) return false
    const num = parseInt(portNum, 10)
    return !isNaN(num) && num >= 1 && num <= 65535
  }

  // Validation methods
  const validateIp = () => {
    if (!ipAddress.value) {
      ipError.value = 'IP address is required'
    } else if (!isValidIp(ipAddress.value)) {
      ipError.value = isLiveServer.value 
        ? 'Please enter a valid IP address (e.g., ***********)' 
        : 'Use "localhost" for local server'
    } else {
      ipError.value = ''
    }
  }

  const validatePort = () => {
    if (!port.value) {
      portError.value = 'Port number is required'
    } else if (!isValidPort(port.value)) {
      portError.value = 'Port must be between 1 and 65535'
    } else {
      portError.value = ''
    }
  }

  // Clear error methods
  const clearIpError = () => {
    if (ipError.value) ipError.value = ''
  }

  const clearPortError = () => {
    if (portError.value) portError.value = ''
  }

  // Handle actions
  const handleTestConnection = async () => {
    if (!isValid.value) return
    
    isTestingConnection.value = true
    connectionStatus.value = null
    connectionTimestamp.value = null
    
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), CONNECTION_TIMEOUT)

      const response = await fetch(`https://${connectionString.value}/api/v1/test-connection`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      connectionStatus.value = data.connection_status
      connectionTimestamp.value = new Date(data.timestamp).toLocaleString()
      
      if (data.connection_status === 'available') {
        // Save successful test results
        localStorage.setItem(LAST_TEST_KEY, JSON.stringify({
          status: data.connection_status,
          timestamp: data.timestamp,
          connectionString: connectionString.value
        }))

        toastSuccess('Connection successful!')
      } else {
        toastWarning('Connection test completed but server is not available')
      }
      
    } catch (error: any) {
      console.error("connection test string: ", `https://${connectionString.value}/api/v1/test-connection`)
      console.error('Connection test failed:', error)
      connectionStatus.value = 'error'
      
      // Specific error message for timeout
      if (error.name === 'AbortError') {
        toastDanger('Connection timed out. Please check your network settings.')
      } else {
        toastDanger('Connection test failed. Please check your network settings.')
      }
    } finally {
      isTestingConnection.value = false
    }
  }

  // New function to parse connection string
  const parseConnectionString = (str: string) => {
    try {
      const url = new URL(str)
      return {
        ip: url.hostname,
        port: url.port || '3002'
      }
    } catch (error) {
      // Handle non-URL format like "***********:3002"
      if (str.includes(':')) {
        const [ip, port] = str.split(':')
        return { ip: ip.trim(), port: port.trim() }
      }
      return null
    }
  }

  // Load configuration from localStorage
  const loadConfiguration = () => {
    // Load caching state first
    loadCachingState()

    // Load saved connection string
    const savedConnection = localStorage.getItem('offline_connection_string')
    if (savedConnection) {
      const parsed = parseConnectionString(savedConnection)
      if (parsed) {
        ipAddress.value = parsed.ip
        port.value = parsed.port
      }
    }

    // Load saved server mode preference
    const savedUseMODS = localStorage.getItem('useMODS')
    if (savedUseMODS !== null) {
      // useMODS = true means LAN Server, false means Live Server
      isLiveServer.value = savedUseMODS === 'false'
    }

    // Load last successful test results
    const lastTest = localStorage.getItem(LAST_TEST_KEY)
    if (lastTest) {
      try {
        const testData = JSON.parse(lastTest)
        // Only show last test if it matches current connection string
        if (testData.connectionString === connectionString.value) {
          connectionStatus.value = testData.status
          connectionTimestamp.value = new Date(testData.timestamp).toLocaleString()
        }
      } catch (error) {
        console.error('Error parsing last test data:', error)
      }
    }
  }

  // Save function
  const handleSave = () => {
  if (isValid.value) {
    const userConfirmed = window.confirm('Are you sure you want to save this network configuration?');

    if (userConfirmed) {
      const connectionStr = fullConnectionUrl.value;
      persistedConnectionString.value = connectionStr;

      updateConnectionString({
        connection_string_id: 1,
        connection_string: connectionStr,
      });
      toastSuccess('Network configuration saved successfully!');
    }
  }
};


  // onMounted hook
  onMounted(async () => {
    // Load configuration first
    loadConfiguration()
    
    // Validate loaded values
    await nextTick(() => {
      validateIp()
      validatePort()
    })

    // Set initial useMODS value based on current server mode
    updateUseMODS(isLiveServer.value)

    // Mark as initialized to allow watchers to work
    isInitialized.value = true
  })

  const handleServerModeChange = async (event: any) => {
    const newValue = event.target.checked
    const message = newValue 
      ? 'Switch to Live Server mode? This will use internet connection for sync.'
      : 'Switch to LAN Server mode? This will use local network for sync.'
      
    if (confirm(message)) {
      isLiveServer.value = newValue
      
      // Update useMODS localStorage value
      updateUseMODS(newValue)
      
      if (!newValue) {
        ipAddress.value = '0.0.0.0'
        ipError.value = ''
      }
      toastSuccess(`Switched to ${newValue ? 'Live Server' : 'LAN Server'} mode`)
    } else {
      // Revert the checkbox if user cancels
      event.target.checked = !newValue
      isLiveServer.value = !newValue
      toastWarning('Server mode change cancelled')
    }
  }
// Replace the caching-related code with this explicit implementation

// Reactive ref for caching state
const storeCachedRecords = ref(false)

// Function to load caching state from localStorage
const loadCachingState = () => {
  const saved = localStorage.getItem('storeCachedRecords')
  if (saved !== null) {
    storeCachedRecords.value = saved === 'true'
  } else {
    // Set default value if not found in localStorage
    storeCachedRecords.value = false
    localStorage.setItem('storeCachedRecords', 'false')
  }
  console.log('Loaded caching state:', storeCachedRecords.value)
}

// Function to save caching state to localStorage
const saveCachingState = (value: boolean) => {
  localStorage.setItem('storeCachedRecords', value.toString())
  console.log('Saved caching state to localStorage:', value)
}

// Updated handleCacheToggleChange function
const handleCacheToggleChange = async (event: any) => {
  const newValue = event.target.checked
  const message = newValue
    ? 'Enable caching? This will store patient records locally for offline access.'
    : 'Disable caching? Patient records will not be stored locally.'

  if (confirm(message)) {
    console.log(`Caching state changed to ${newValue}`)
    // Update the reactive ref
    storeCachedRecords.value = newValue
    // Save to localStorage
    saveCachingState(newValue)
    toastSuccess(`Patient record caching ${newValue ? 'enabled' : 'disabled'}`)
  } else {
    // Revert the checkbox if user cancels
    event.target.checked = storeCachedRecords.value
    toastWarning('Caching setting change cancelled')
  }
}

// Optional: Add a watcher to monitor changes
watch(storeCachedRecords, (newVal, oldVal) => {
  console.log(`Caching state changed from ${oldVal} to ${newVal}`)
}, { immediate: false })

// Make sure to update your reactive data section to include:
// const storeCachedRecords = ref(false) // Add this line to your reactive data section
</script>
  
<style scoped>
.network-config {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  /* border-radius: 12px;
  background-color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); */
  font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
}

.header {
  text-align: left;
  margin-bottom: 32px;
}

.title-section .title {
  color: #2c3e50;
  margin: 0 0 8px 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.title-section .subtitle {
  color: #666;
  font-size: 1.2rem;
  font-weight: 600;
}

.controls-section {
  margin-bottom: 24px;
}

.toggle-container {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  height: 100%;
  border: 1px solid #e9ecef;
  margin-bottom: 16px;
}

.toggle-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.toggle-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-label {
  font-size: 1.1rem;
  color: #495057;
  font-weight: 600;
  margin-bottom: 8px;
}

.toggle-status {
  font-size: 1rem;
  font-weight: 600;
  color: #2fa543;
}

.switch {
  position: relative;
  display: inline-block;
  width: 64px;
  height: 32px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 32px;
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
}

.slider:before {
  position: absolute;
  content: "";
  height: 24px;
  width: 24px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input:checked + .slider {
  background-color: #42b983;
}

input:checked + .slider:before {
  transform: translateX(32px);
}

.slider:hover {
  background-color: #b3b3b3;
}

input:checked + .slider:hover {
  background-color: #3aa876;
}

.mode-description {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #6c757d;
  line-height: 1.4;
}

.network-settings {
  margin-bottom: 24px;
}

.input-group {
  margin-bottom: 16px;
}

.label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
  font-size: 0.95rem;
}

.input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
  box-sizing: border-box;
}

.input:focus {
  outline: none;
  border-color: #42b983;
  box-shadow: 0 0 0 2px rgba(66, 185, 131, 0.2);
  background-color: white;
}

.input.error {
  border-color: #ff4757;
}

.error-message {
  color: #ff4757;
  font-size: 0.85rem;
  margin-top: 6px;
  display: block;
}

.connection-section {
  margin-bottom: 24px;
}

.connection-result {
  background-color: #f0f7f4;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #42b983;
  font-size: 0.95rem;
  color: #2c3e50;
}

.connection-string {
  margin-bottom: 16px;
  word-break: break-all;
  font-size: 1.2rem;
  font-weight: 600;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  padding-top: 16px;
  border-top: 1px solid #e1e8e3;
  font-size: 1.2rem;
  font-weight: 600;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.status-indicator::before {
  content: "";
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.available {
  background-color: #e6f4ea;
  color: #1e7e34;
}

.status-indicator.available::before {
  background-color: #28a745;
}

.status-indicator.error {
  background-color: #fde8e8;
  color: #c53030;
}

.status-indicator.error::before {
  background-color: #dc3545;
}

.timestamp {
  font-size: 1.1rem;
  font-weight: 600;
  color: #e44b0f;
}

.actions-section {
  margin-top: 32px;
}

ion-button {
  --padding-top: 16px;
  --padding-bottom: 16px;
  --padding-start: 24px;
  --padding-end: 24px;
  --border-radius: 8px;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-weight: 600;
  font-size: 1rem;
  text-transform: none;
  letter-spacing: 0;
  margin-bottom: 12px;
  height: 48px;
}

ion-button.test-btn {
  --background: #3498db;
  --background-hover: #2980b9;
  --background-activated: #2980b9;
  --background-focused: #2980b9;
}

ion-button.save-btn {
  --background: #42b983;
  --background-hover: #369f76;
  --background-activated: #369f76;
  --background-focused: #369f76;
}

ion-button.disabled {
  --background: #bdc3c7;
  --background-hover: #bdc3c7;
  --background-activated: #bdc3c7;
  --background-focused: #bdc3c7;
  --opacity: 0.7;
  pointer-events: none;
}

/* Remove number input arrows */
.input[type="number"]::-webkit-outer-spin-button,
.input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.input[type="number"] {
  -moz-appearance: textfield;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .network-config {
    padding: 16px;
  }

  .title-section .title {
    font-size: 1.5rem;
  }

  .title-section .subtitle {
    font-size: 0.9rem;
  }

  .toggle-container {
    padding: 16px;
    margin-bottom: 12px;
  }

  .toggle-section {
    gap: 8px;
  }

  .toggle-label {
    font-size: 1rem;
  }

  .connection-result {
    padding: 16px;
  }

  .connection-status {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .actions-section ion-col {
    margin-bottom: 8px;
  }
}

@media (max-width: 480px) {
  .network-config {
    padding: 12px;
  }

  .title-section .title {
    font-size: 1.3rem;
  }

  .toggle-container {
    padding: 12px;
  }

  .toggle-wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .mode-description {
    font-size: 0.85rem;
  }

  .input {
    padding: 10px 12px;
    font-size: 14px;
  }

  .connection-result {
    padding: 12px;
  }

  .connection-string {
    font-size: 0.9rem;
  }
}
</style>