import { AppEncounterService } from '@/services/app_encounter_service';
import { toastSuccess } from '@/utils/Alerts';
import { ref, reactive } from "vue";

type answer = "yes" | "no";

export const usePregnancyOrBreastfeeding = () => {
    const pregnancyBreastFeedingData = reactive({
        isPregnant: null as answer | null,
        isBreastFeeding: null as answer | null,
    });

    const isPregnancyBreastfeedingLoading = ref<boolean>(false);
    const isPregnancyBreastfeedingSaved = ref<boolean>(false);
    const validationErrors = ref<string[]>([]);

    const validatePregnancyBreastFeeding = (): boolean => {
        validationErrors.value = [];
        if (pregnancyBreastFeedingData.isPregnant === null) {
            validationErrors.value.push("Pregnancy status is required.");
        };

        if (pregnancyBreastFeedingData.isBreastFeeding === null) {
            validationErrors.value.push("Breastfeeding status is required.");
        }
        return validationErrors.value.length === 0;
    };

    const buildPregnancyBreastFeedingObs = async (
        patientID: number,
        providerID: number,
        locationID: string
    ): Promise<any[]> => {
        const encounterService = new AppEncounterService(patientID, 53, providerID, locationID);
        const observations: any[] = [];

        if (pregnancyBreastFeedingData.isPregnant !== null) {
            const pregnancyObs = await encounterService.buildValueCoded(
                'Is patient pregnant?',
                pregnancyBreastFeedingData.isPregnant === 'yes' ? 'Yes' : 'No'
            );
            observations.push(pregnancyObs);
        }

        if (pregnancyBreastFeedingData.isBreastFeeding !== null) {
            const breastfeedingObs = await encounterService.buildValueCoded(
                'Is patient breast feeding?',
                pregnancyBreastFeedingData.isBreastFeeding === 'yes' ? 'Yes' : 'No'
            );
            observations.push(breastfeedingObs);
        }

        return observations;
    }

    const savePregnancyBreastFeeding = async (
        patientID: number,
        providerID: number,
        locationID: string
    ): Promise<boolean> => {
        if (!validatePregnancyBreastFeeding()) {
            console.error("Validation failed:", validationErrors.value);
            return false;
        }

        isPregnancyBreastfeedingLoading.value = true;
        try {
            const observations = await buildPregnancyBreastFeedingObs(patientID, providerID, locationID);

            if (observations.length > 0) {
                const encounterService = new AppEncounterService(patientID, 53, providerID, locationID);
                await encounterService.saveObservationList(observations);
                isPregnancyBreastfeedingSaved.value = true;
                toastSuccess("Pregnancy or Breast Feeding data saved successfully");
                return true;
            } else {
                console.warn("No observations to save");
                return false;
            }
        } catch (error) {
            console.error("Error saving data:", error);
            return false;
        } finally {
            isPregnancyBreastfeedingLoading.value = false;
        }
    };

    return {
        pregnancyBreastFeedingData,
        isPregnancyBreastfeedingLoading,
        validationErrors,
        isPregnancyBreastfeedingSaved,
        validatePregnancyBreastFeeding,
        savePregnancyBreastFeeding,
        buildPregnancyBreastFeedingObs,
    };
}
